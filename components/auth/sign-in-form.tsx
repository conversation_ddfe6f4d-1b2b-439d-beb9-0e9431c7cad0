'use client';

import { useActionState, useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { LogIn, Lock, Mail, AlertCircle, Eye, EyeOff } from 'lucide-react';
import Link from 'next/link';
import { authenticateUser } from '@/app/(auth)/actions';
import { useCSRFToken, CSRFTokenInput } from '../../lib/csrf';
import { AuthResult } from '@/types/auth';

const initialState: AuthResult = {
  success: false,
  error: undefined,
  nextStep: undefined,
  email: undefined,
};

export function SignInForm() {
  const [state, formAction] = useActionState(authenticateUser, initialState);
  const [email, setEmail] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const { csrfToken, loading: csrfLoading, error: csrfError } = useCSRFToken();
  const router = useRouter();
  const searchParams = useSearchParams();
  const redirect = searchParams.get('redirect') || '/';
  const successMessage = searchParams.get('message');

  useEffect(() => {
    if (state.success) {
      const redirectPath = state.redirectTo || redirect;
      router.push(redirectPath);
      router.refresh();
    }
  }, [state.success, state.redirectTo, router, redirect]);

  const displayError = state.error || csrfError;

  return (
    <div className="w-full bg-gradient-to-br from-[#2a1f3d] via-[#1f1727] to-[#1a1420] rounded-3xl shadow-2xl border border-purple-800/20 backdrop-blur-sm">
      <div className="p-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center w-16 h-16 rounded-2xl   mb-6 mx-auto ">
            <img src="/qbraid_logo.png" alt="qBraid" className="h-16 w-auto opacity-90" />
          </div>
          <h1 className="text-3xl font-bold text-white mb-2">Sign in to qBraid</h1>
          <p className="text-slate-400 text-sm">
            Access your quantum computing dashboard and manage your devices
          </p>
        </div>

        {/* Form */}
        <form action={formAction} className="space-y-5">
          <CSRFTokenInput csrfToken={csrfToken} />

          {/* Success Message */}
          {successMessage && (
            <div className="flex items-start gap-3 p-4 rounded-xl bg-green-500/10 border border-green-500/20 backdrop-blur-sm">
              <LogIn className="w-5 h-5 text-green-400 flex-shrink-0 mt-0.5" />
              <p className="text-green-400 text-sm">{successMessage}</p>
            </div>
          )}

          {/* Error Alert */}
          {displayError && (
            <div className="flex items-start gap-3 p-4 rounded-xl bg-red-500/10 border border-red-500/20 backdrop-blur-sm">
              <AlertCircle className="w-5 h-5 text-red-400 flex-shrink-0 mt-0.5" />
              <div className="flex-1">
                <p className="text-red-400 text-sm">
                  {displayError}
                  {state.requiresVerification && (
                    <>
                      <br />
                      <Link
                        href={`/verify${email ? `?email=${encodeURIComponent(email)}` : ''}`}
                        className="underline hover:no-underline"
                      >
                        Go to verification page →
                      </Link>
                    </>
                  )}
                </p>
              </div>
            </div>
          )}

          {/* Email */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-slate-300 block">Email Address</label>
            <div className="relative group">
              <Mail className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-slate-500 group-focus-within:text-purple-400 transition-colors" />
              <input
                name="email"
                type="email"
                placeholder="Enter your email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className="w-full pl-10 pr-4 py-3.5 rounded-xl border border-slate-600/50 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500 bg-slate-800/50 text-white placeholder:text-slate-500 transition-all duration-200 hover:border-slate-500"
              />
            </div>
          </div>

          {/* Password */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-slate-300 block">Password</label>
            <div className="relative group">
              <Lock className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-slate-500 group-focus-within:text-purple-400 transition-colors" />
              <input
                name="password"
                type={showPassword ? 'text' : 'password'}
                placeholder="Enter your password"
                required
                className="w-full pl-10 pr-12 py-3.5 rounded-xl border border-slate-600/50 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500 bg-slate-800/50 text-white placeholder:text-slate-500 transition-all duration-200 hover:border-slate-500"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-slate-500 hover:text-slate-300 transition-colors"
              >
                {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              </button>
            </div>
          </div>

          {/* Forgot Password Link */}
          <div className="flex justify-end">
            <Link
              href="/forgot-password"
              className="text-sm text-purple-400 hover:text-purple-300 hover:underline font-medium transition-colors"
            >
              Forgot password?
            </Link>
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={csrfLoading || !csrfToken}
            className="w-full bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-500 hover:to-purple-600 text-white font-semibold py-4 rounded-xl shadow-lg hover:shadow-xl hover:shadow-purple-500/25 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-[1.01] active:scale-[0.99] mt-6 disabled:hover:scale-100"
          >
            {csrfLoading ? (
              <div className="flex items-center justify-center gap-2">
                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                Loading...
              </div>
            ) : (
              'Sign In'
            )}
          </button>
        </form>

        {/* Divider */}
        <div className="flex items-center my-8">
          <div className="flex-1 border-t border-slate-600/50" />
          <span className="px-4 text-xs text-slate-500 font-medium">Or continue with</span>
          <div className="flex-1 border-t border-slate-600/50" />
        </div>

        {/* Social Buttons */}
        <div className="grid  gap-3 mb-8">
          <button
            type="button"
            className="flex items-center justify-center h-12 rounded-xl border border-slate-600/50 bg-slate-800/30 hover:bg-slate-700/50 transition-all duration-200 group"
          >
            <img
              src="https://www.svgrepo.com/show/475656/google-color.svg"
              alt="Google"
              className="w-5 h-5 group-hover:scale-110 transition-transform"
            />
          </button>
        </div>

        {/* Sign Up Link */}
        <p className="text-center text-sm text-slate-400">
          Don't have an account?{' '}
          <Link
            href="/signup"
            className="text-purple-400 hover:text-purple-300 font-semibold hover:underline transition-colors"
          >
            Sign up
          </Link>
        </p>
        <p className="text-center text-sm text-slate-400 mt-10">
          &copy; 2025 qBraid Co. All rights reserved.
        </p>
      </div>
    </div>
  );
}
